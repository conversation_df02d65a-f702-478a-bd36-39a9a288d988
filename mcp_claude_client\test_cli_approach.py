#!/usr/bin/env python3
"""
Test using the FastMCP CLI approach.
"""

import asyncio
import subprocess
import sys
import os
from pathlib import Path

from fastmcp import Client


async def test_cli_approach():
    """Test using the FastMCP CLI approach."""
    print("🔌 Testing FastMCP CLI approach...")
    
    # Start the MCP server using the FastMCP CLI
    server_process = None
    
    try:
        # Get the absolute path to the server
        server_path = str(Path(__file__).parent.parent / "simple_mcp_server.py")
        print(f"📁 Server path: {server_path}")
        
        # Start the server with the FastMCP CLI in a separate process
        server_process = subprocess.Popen(
            ["fastmcp", "run", server_path],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait a moment for the server to start
        await asyncio.sleep(2)
        
        # Create a client that connects to the server
        client_config = {
            "mcpServers": {
                "simple-mcp-server": {
                    "command": "fastmcp",
                    "args": ["run", server_path]
                }
            }
        }
        
        client = Client(client_config)
        
        async with client:
            print("✅ Connected successfully!")
            
            # List tools
            tools = await client.list_tools()
            print(f"📋 Found {len(tools)} tools:")
            for tool in tools:
                print(f"  - {tool.name}: {tool.description}")
            
            # Test a tool
            result = await client.call_tool("greet", {"name": "Test User"})
            print(f"🔧 Tool result: {result}")
            
            return True
            
    except Exception as e:
        print(f"❌ Failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up the server process
        if server_process:
            server_process.terminate()
            try:
                server_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                server_process.kill()


if __name__ == "__main__":
    print("🧪 Testing MCP Client with CLI Approach")
    print("=" * 40)
    
    success = asyncio.run(test_cli_approach())
    print(f"\n{'✅ Success!' if success else '❌ Failed!'}")
