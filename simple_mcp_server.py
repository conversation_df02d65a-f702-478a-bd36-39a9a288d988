#!/usr/bin/env python3
"""
Simple MCP Server using FastMCP 2.0

This server provides 3 sample tools:
1. greet - A simple greeting tool
2. calculate_sum - A calculator tool that adds two numbers
3. get_current_time - A tool that returns the current date and time
"""

import asyncio
from datetime import datetime
from typing import Literal
from fastmcp import FastMCP

# Create the FastMCP server instance
mcp = FastMCP("Simple MCP Server")


@mcp.tool
def greet(name: str, greeting_type: Literal["formal", "casual"] = "casual") -> str:
    """
    Generate a personalized greeting message.
    
    Args:
        name: The name of the person to greet
        greeting_type: The type of greeting (formal or casual)
    
    Returns:
        A personalized greeting message
    """
    if greeting_type == "formal":
        return f"Good day, {name}. It's a pleasure to meet you."
    else:
        return f"Hey there, {name}! Nice to meet you!"


@mcp.tool
def calculate_sum(a: float, b: float) -> dict:
    """
    Calculate the sum of two numbers.
    
    Args:
        a: The first number
        b: The second number
    
    Returns:
        A dictionary containing the calculation details
    """
    result = a + b
    return {
        "operation": "addition",
        "operand_a": a,
        "operand_b": b,
        "result": result,
        "calculation": f"{a} + {b} = {result}"
    }


@mcp.tool
async def get_current_time(timezone: str = "UTC", format_type: Literal["iso", "readable"] = "readable") -> dict:
    """
    Get the current date and time.
    
    Args:
        timezone: The timezone (currently only supports UTC)
        format_type: The format of the returned time (iso or readable)
    
    Returns:
        A dictionary containing current time information
    """
    now = datetime.now()
    
    if format_type == "iso":
        formatted_time = now.isoformat()
    else:
        formatted_time = now.strftime("%A, %B %d, %Y at %I:%M:%S %p")
    
    return {
        "timezone": timezone,
        "timestamp": now.timestamp(),
        "formatted_time": formatted_time,
        "iso_format": now.isoformat(),
        "year": now.year,
        "month": now.month,
        "day": now.day,
        "hour": now.hour,
        "minute": now.minute,
        "second": now.second
    }


# Test client functionality (optional - for testing)
async def test_server():
    """Test the server tools using a FastMCP client."""
    from fastmcp import Client
    
    client = Client(mcp)
    
    async with client:
        # Test the greet tool
        print("Testing greet tool:")
        result1 = await client.call_tool("greet", {"name": "Alice"})
        print(f"Casual greeting: {result1}")
        
        result2 = await client.call_tool("greet", {"name": "Bob", "greeting_type": "formal"})
        print(f"Formal greeting: {result2}")
        
        # Test the calculate_sum tool
        print("\nTesting calculate_sum tool:")
        result3 = await client.call_tool("calculate_sum", {"a": 15.5, "b": 24.3})
        print(f"Sum calculation: {result3}")
        
        # Test the get_current_time tool
        print("\nTesting get_current_time tool:")
        result4 = await client.call_tool("get_current_time", {})
        print(f"Current time (readable): {result4}")
        
        result5 = await client.call_tool("get_current_time", {"format_type": "iso"})
        print(f"Current time (ISO): {result5}")


def main():
    """Main entry point for the MCP server."""
    import argparse

    parser = argparse.ArgumentParser(description="Simple MCP Server")
    parser.add_argument("--http", action="store_true", help="Run as HTTP server")
    parser.add_argument("--port", type=int, default=8000, help="Port for HTTP server (default: 8000)")
    parser.add_argument("--host", default="localhost", help="Host for HTTP server (default: localhost)")
    parser.add_argument("--test", action="store_true", help="Run test client")

    args = parser.parse_args()

    # Check for HTTP mode argument
    if args.http:
        print(f"🌐 Starting HTTP MCP server at http://{args.host}:{args.port}")
        print("Press Ctrl+C to stop the server")
        try:
            mcp.run_http(host=args.host, port=args.port)
        except AttributeError:
            print("❌ HTTP mode not available in this FastMCP version")
            print("💡 Use 'fastmcp run simple_mcp_server.py:mcp --transport http' instead")
    elif args.test:
        # Run the test client
        asyncio.run(test_server())
    else:
        # Run the standard STDIO MCP server
        print("🚀 Starting MCP server with STDIO transport")
        print("Press Ctrl+C to stop the server")
        mcp.run()


if __name__ == "__main__":
    main()
