#!/usr/bin/env python3
"""
Test to see if we can get a working MCP client connection.
"""

import asyncio
import sys
from pathlib import Path

# Try the exact same approach as the working test_client.py
sys.path.insert(0, str(Path(__file__).parent.parent))

from fastmcp import Client


async def test_simple_connection():
    """Test the simplest possible connection."""
    print("🔌 Testing simple MCP connection...")
    
    try:
        # Use the exact same approach as the working test_client.py
        # but from the parent directory
        client = Client("../simple_mcp_server.py")
        
        async with client:
            print("✅ Connected successfully!")
            
            # List tools
            tools = await client.list_tools()
            print(f"📋 Found {len(tools)} tools:")
            for tool in tools:
                print(f"  - {tool.name}: {tool.description}")
            
            # Test a tool
            result = await client.call_tool("greet", {"name": "Test User"})
            print(f"🔧 Tool result: {result}")
            
            return True
            
    except Exception as e:
        print(f"❌ Failed: {e}")
        return False


async def test_absolute_path():
    """Test with absolute path."""
    print("\n🔌 Testing with absolute path...")
    
    try:
        # Use absolute path
        server_path = str(Path(__file__).parent.parent / "simple_mcp_server.py")
        print(f"📁 Server path: {server_path}")
        
        client = Client(server_path)
        
        async with client:
            print("✅ Connected successfully!")
            
            # List tools
            tools = await client.list_tools()
            print(f"📋 Found {len(tools)} tools:")
            for tool in tools:
                print(f"  - {tool.name}: {tool.description}")
            
            return True
            
    except Exception as e:
        print(f"❌ Failed: {e}")
        return False


if __name__ == "__main__":
    print("🧪 Testing MCP Client Connection")
    print("=" * 40)
    
    async def main():
        success1 = await test_simple_connection()
        success2 = await test_absolute_path()
        
        if success1 or success2:
            print("\n✅ At least one connection method worked!")
            return True
        else:
            print("\n❌ All connection methods failed")
            return False
    
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
