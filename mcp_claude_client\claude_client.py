"""
Claude API client integration for the MCP Claude Client.
"""

import logging
from typing import List, Dict, Any, Optional, Union
import anthropic
from anthropic.types import Message, MessageParam, ToolParam, ToolUseBlock, TextBlock

from .config import ClientConfig
from .exceptions import ClaudeAPIError


logger = logging.getLogger(__name__)


class ClaudeClient:
    """Client for interacting with the Claude API."""
    
    def __init__(self, config: ClientConfig):
        """Initialize the Claude client with configuration."""
        self.config = config
        
        if not config.claude_api_key:
            raise ClaudeAPIError("Claude API key is required")
        
        try:
            self._client = anthropic.Anthropic(api_key=config.claude_api_key)
            logger.info("Claude API client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Claude API client: {e}")
            raise Claude<PERSON>IError(f"Failed to initialize Claude API client: {e}")
    
    def _convert_mcp_tools_to_claude_format(self, mcp_tools: List[Any]) -> List[ToolParam]:
        """Convert MCP tools to Claude API tool format."""
        claude_tools = []
        
        for tool in mcp_tools:
            try:
                # Extract tool information
                tool_name = tool.name
                tool_description = tool.description or f"Tool: {tool_name}"
                
                # Convert input schema to Claude format
                input_schema = {}
                if hasattr(tool, 'inputSchema') and tool.inputSchema:
                    input_schema = tool.inputSchema
                elif hasattr(tool, 'input_schema') and tool.input_schema:
                    input_schema = tool.input_schema
                
                claude_tool: ToolParam = {
                    "name": tool_name,
                    "description": tool_description,
                    "input_schema": input_schema or {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                }
                
                claude_tools.append(claude_tool)
                logger.debug(f"Converted MCP tool '{tool_name}' to Claude format")
                
            except Exception as e:
                logger.warning(f"Failed to convert MCP tool to Claude format: {e}")
                continue
        
        logger.info(f"Converted {len(claude_tools)} MCP tools to Claude format")
        return claude_tools
    
    async def send_message(
        self,
        messages: List[MessageParam],
        tools: Optional[List[ToolParam]] = None,
        system_prompt: Optional[str] = None,
        max_tokens: Optional[int] = None,
    ) -> Message:
        """Send a message to Claude with optional tools."""
        try:
            # Prepare request parameters
            request_params = {
                "model": self.config.claude_model,
                "max_tokens": max_tokens or self.config.claude_max_tokens,
                "messages": messages,
            }
            
            if system_prompt:
                request_params["system"] = system_prompt
            
            if tools:
                request_params["tools"] = tools
            
            # logger.info(f"Sending message to Claude (model: {self.config.claude_model})")
            logger.debug(f"Request parameters: {request_params}")
            
            # Send request to Claude
            response = self._client.messages.create(**request_params)
            
            # logger.info("Received response from Claude")
            # logger.debug(f"Response: {response}")
            
            return response
            
        except anthropic.APIError as e:
            logger.error(f"Claude API error: {e}")
            raise ClaudeAPIError(f"Claude API error: {e}")
        except Exception as e:
            logger.error(f"Unexpected error calling Claude API: {e}")
            raise ClaudeAPIError(f"Unexpected error calling Claude API: {e}")
    
    def extract_tool_calls(self, message: Message) -> List[Dict[str, Any]]:
        """Extract tool calls from Claude's response."""
        tool_calls = []
        
        for content_block in message.content:
            if isinstance(content_block, ToolUseBlock):
                tool_call = {
                    "id": content_block.id,
                    "name": content_block.name,
                    "input": content_block.input,
                }
                tool_calls.append(tool_call)
                # logger.debug(f"Extracted tool call: {tool_call}")
        
        # logger.info(f"Extracted {len(tool_calls)} tool calls from Claude response")
        return tool_calls
    
    def extract_text_content(self, message: Message) -> str:
        """Extract text content from Claude's response."""
        text_parts = []
        
        for content_block in message.content:
            if isinstance(content_block, TextBlock):
                text_parts.append(content_block.text)
        
        return "\n".join(text_parts)
    
    def create_tool_result_message(self, tool_call_id: str, result: Any) -> MessageParam:
        """Create a tool result message for Claude."""
        return {
            "role": "user",
            "content": [
                {
                    "type": "tool_result",
                    "tool_use_id": tool_call_id,
                    "content": str(result) if not isinstance(result, str) else result,
                }
            ],
        }
    
    def create_user_message(self, content: str) -> MessageParam:
        """Create a user message for Claude."""
        return {
            "role": "user",
            "content": content,
        }
    
    def create_assistant_message(self, content: str) -> MessageParam:
        """Create an assistant message for Claude."""
        return {
            "role": "assistant", 
            "content": content,
        }
    
    def format_tools_for_system_prompt(self, tools: List[Any]) -> str:
        """Format available tools for inclusion in system prompt."""
        if not tools:
            return "No tools are currently available."
        
        tool_descriptions = []
        for tool in tools:
            name = tool.name
            description = tool.description or "No description available"
            tool_descriptions.append(f"- {name}: {description}")
        
        return f"Available tools:\n" + "\n".join(tool_descriptions)
    
    @property
    def model(self) -> str:
        """Get the current Claude model."""
        return self.config.claude_model
    
    @property
    def max_tokens(self) -> int:
        """Get the current max tokens setting."""
        return self.config.claude_max_tokens
