#!/usr/bin/env python3
"""
Test MCP connection using proper MCP configuration format.
"""

import asyncio
import json
from fastmcp import Client


async def test_mcp_config():
    """Test MCP connection using configuration file."""
    print("🔌 Testing MCP connection with config file...")
    
    try:
        # Load the MCP configuration
        with open("mcp_config.json", "r") as f:
            config = json.load(f)
        
        print(f"📋 Config: {config}")
        
        # Create client with the configuration
        client = Client(config)
        
        async with client:
            print("✅ Connected successfully!")
            
            # List tools
            tools = await client.list_tools()
            print(f"📋 Found {len(tools)} tools:")
            for tool in tools:
                print(f"  - {tool.name}: {tool.description}")
            
            # Test a tool
            result = await client.call_tool("greet", {"name": "Test User"})
            print(f"🔧 Tool result: {result}")
            
            return True
            
    except Exception as e:
        print(f"❌ Failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(test_mcp_config())
    print(f"\n{'✅ Success!' if success else '❌ Failed!'}")
