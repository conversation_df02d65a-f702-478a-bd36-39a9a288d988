#!/usr/bin/env python3
"""
Simple test to debug the MCP connection issue.
"""

import asyncio
import sys
from pathlib import Path

# Add the parent directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent))

from fastmcp import Client


async def test_direct_fastmcp():
    """Test direct FastMCP client connection."""
    print("🔌 Testing direct FastMCP client connection...")
    
    # Try different connection methods
    server_paths = [
        "simple_mcp_server.py",  # Try from current directory first
        "../simple_mcp_server.py",
        "E:\\Code\\PythonApps\\testMCP\\simple_mcp_server.py",
        str(Path(__file__).parent.parent / "simple_mcp_server.py"),
    ]
    
    for server_path in server_paths:
        print(f"\n📁 Trying server path: {server_path}")
        
        try:
            client = Client(server_path)
            
            async with client:
                print("✅ Connected successfully!")
                
                # List tools
                tools = await client.list_tools()
                print(f"📋 Found {len(tools)} tools:")
                for tool in tools:
                    print(f"  - {tool.name}: {tool.description}")
                
                # Test a tool
                result = await client.call_tool("greet", {"name": "Test"})
                print(f"🔧 Tool result: {result}")
                
                return True
                
        except Exception as e:
            print(f"❌ Failed: {e}")
            continue
    
    return False


async def test_command_based():
    """Test command-based connection."""
    print("\n🔌 Testing command-based connection...")
    
    try:
        # Use command-based connection
        server_config = {
            "command": "python",
            "args": [str(Path(__file__).parent.parent / "simple_mcp_server.py")],
        }
        
        client = Client(server_config)
        
        async with client:
            print("✅ Command-based connection successful!")
            
            # List tools
            tools = await client.list_tools()
            print(f"📋 Found {len(tools)} tools:")
            for tool in tools:
                print(f"  - {tool.name}: {tool.description}")
            
            return True
            
    except Exception as e:
        print(f"❌ Command-based connection failed: {e}")
        return False


if __name__ == "__main__":
    print("🧪 MCP Connection Debug Test")
    print("=" * 40)
    
    async def main():
        success1 = await test_direct_fastmcp()
        success2 = await test_command_based()
        
        if success1 or success2:
            print("\n✅ At least one connection method worked!")
        else:
            print("\n❌ All connection methods failed")
    
    asyncio.run(main())
